#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础检查界面测试脚本
测试新的卡片状态切换功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from main_window import MainWindow

def test_basic_check_interface():
    """测试基础检查界面"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 切换到基础检查分类
    window.current_category = "基础检查"
    window._on_category_button_clicked("基础检查")
    
    print("基础检查界面已启动")
    print("测试说明：")
    print("1. 界面应显示三个分类的检查项目")
    print("2. 恢复窗口模式下每行应显示3个卡片")
    print("3. 双击卡片应切换到内容模式，显示检查细则和相关标准")
    print("4. 内容模式下卡片名称旁边应有勾选框")
    print("5. 勾选后应切换到完成模式，显示大勾选标记")
    print("6. 再次双击已完成卡片应恢复到图片模式")
    
    return app.exec_()

if __name__ == "__main__":
    test_basic_check_interface()
